import { defineStore } from 'pinia'
import { useAPI } from '~/composables/useAPI'
import type { Ref } from 'vue'
import type { ServerResponseError } from '~/interface/common-interface'
import { useNotificationsStore } from './notifications'
import { useTranslateStore } from '~/stores/translate'
import { useAppStore } from '~/stores/app'
import { GoogleAuthProvider, signInWithPopup } from 'firebase/auth'
import dayjs from 'dayjs'

const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

interface User {
    email: string
    is_active: boolean
    is_superuser: boolean
    full_name: string
    id: number
    uuid?: string
    status?: number
    user_token?: UserToken
    user_plan?: UserPlan | null
    user_benefits?: UserBenefit[]
    tokens: number
    current_plan: string
    isGuest?: boolean
    subscription_id?: string
    avatar?: string
}

interface LoginInfo {
    username: string
    password: string
    remember_me: boolean
}
interface ForgotPasswordForm {
    email: string
}

interface SignUpInfo {
    full_name: string
    email: string
    password: string
    invitation_code?: string
    origin?: string
}

interface LoginResponse {
    access_token: string
    type: string
    refresh_token?: string
}

interface UserPlan {
    id: number
    plan: {
        id: string
        name: string
    }
    product: {
        id: string
        name: string
        type: number
    }
    is_subscribe: boolean
    expire_at: string | null
    subscription_id?: string
}

interface UserToken {
    plan_token: number
    purchased_token: number
    locked_token: number
    available_token: number
    expire_at: string | null
}

// "user_benefits": [
//     {
//         "id": 1,
//         "product": {
//             "id": "BP0001",
//             "name": "GPT-4o-mini one year unlimited",
//             "type": 3,
//             "max_quantity": 1,
//             "max_monthly_total_file": null,
//             "max_file_size": null,
//             "price_divide_100": 10,
//             "base_token": 0,
//             "bonus_token": 0,
//             "days_of_valid": 365,
//             "paypal_plan_id": null,
//             "usable_gpt_versions": null
//         },
//         "expire_at": "2026-09-03T13:35:08"
//     }
// ],
interface UserBenefit {
    id: number
    product: {
        id: string
        name: string
        type: number
        days_of_valid: number
    }
    expire_at: string
}

interface UserInfoResponse {
    email: string
    is_active: boolean
    is_superuser: boolean
    full_name: string
    id: number
    uuid: string
    status?: number
    user_token?: UserToken
    user_plan?: UserPlan | null
}

interface ForgotPasswordResponse {
    success?: boolean
    detail?: {
        error_code: string
        error_message: string
    }
}

interface ResetPasswordResponse {
    success?: boolean
    email?: string
    detail?: {
        error_code: string
        error_message: string
    }
}

export const useAuthStore = defineStore('auth', {
    state: () => ({
        user: undefined as User | undefined,
        isLoggingIn: false,
        isSigningUp: false,
        isError: false,
        signInError: null as ServerResponseError | null,
        registerError: null as ServerResponseError | null,
        forgotPasswordError: null as ServerResponseError | null,
        resetPasswordError: null as ServerResponseError | null,
        resetPasswordEmail: '' as string,
        activateError: null as ServerResponseError | null,
        isResendingActivation: false,
        isExpired: false,
    }),

    getters: {
        isLoggedIn: (state) => state.user,
        isNotEnoughTokens: (state) => (state.user ? !state.user?.tokens || state.user?.tokens <= 0 : false),
        isUnverified: (state) => !state.user?.is_active,
        // isUnverified: (state) => true,
        currentSubscriptionPlan: (state) =>
            state.user?.user_plan?.subscription_id ? state.user?.user_plan?.product?.id : null,

        isUsingFreePlan: (state) => state.user?.current_plan === 'FP0001',
        showAds: (state) =>
            (state.user?.current_plan === 'FP0001' || !state.user?.current_plan) &&
            useRuntimeConfig().public.features?.ads,
        hasActiveExtendFeature: (state) => {
            if (!state.user?.user_benefits) return false

            const extendFeatureBenefit = state.user.user_benefits.find(
                benefit => benefit.product.id === 'BP0001'
            )

            if (!extendFeatureBenefit) return false

            // Check if not expired
            const now = dayjs()
            const expireDate = dayjs(extendFeatureBenefit.expire_at)

            return expireDate.isAfter(now)
        },
    },

    actions: {
        async login(loginInfo: LoginInfo) {
            this.isLoggingIn = true
            this.isError = false
            this.signInError = null

            const { data, error }: { data: Ref<LoginResponse>; error: any } = await useAPI('login-v2', {
                method: 'POST',
                body: loginInfo,
                server: false,
            })
            this.isLoggingIn = false

            if (data.value?.access_token) {
                localStorage.setItem('access_token', data.value.access_token)
                if (data.value?.refresh_token) {
                    localStorage.setItem('refresh_token', data.value.refresh_token)
                }

                return true
            }

            if (error.value && error.value.data) {
                console.log(error.value.data)
                this.signInError = error.value.data as ServerResponseError
                return false
            }
            //
            // this.isError = true
            // return false
        },
        async signup(signUpInfo: SignUpInfo) {
            this.isSigningUp = true
            this.isError = false
            this.registerError = null
            if (!signUpInfo.invitation_code) {
                delete signUpInfo.invitation_code
            }
            const { data, error }: { data: Ref<LoginResponse>; error: any } = await useAPI('signup', {
                method: 'POST',
                body: signUpInfo,
                lazy: true,
                server: false,
            })
            this.isSigningUp = false

            if (data.value?.access_token) {
                localStorage.setItem('access_token', data.value.access_token)
                return true
            }

            if (error.value && error.value.data?.detail) {
                console.log(error.value.data?.detail)
                this.registerError = error.value.data as ServerResponseError
                return false
            }
            //
            // if (error.value && error.value.data?.detail) {
            //     this.registerError = error.value.data as ServerResponseError
            //     return false
            // }
            //
            // if (data.value) {
            //     return true
            // }
            //
            // this.isError = true
            // return false
        },
        async forgotPassword(payload: ForgotPasswordForm) {
            this.isError = false

            const { data, error }: { data: Ref<ForgotPasswordResponse>; error: any } = await useAPI(
                'password-recovery',
                {
                    method: 'POST',
                    body: payload,
                    // lazy: true,
                    server: false,
                }
            )

            if (data.value && data.value.success) {
                this.forgotPasswordError = null
                console.log('this.forgotPasswordError', this.forgotPasswordError)
                return true
            }

            if (error.value && error.value.data?.detail) {
                console.log(error.value.data?.detail)
                this.forgotPasswordError = error.value.data as ServerResponseError
                return false
            }
        },
        async resetPassword(payload: object) {
            this.isError = false

            const { data, error }: { data: Ref<ResetPasswordResponse>; error: any } = await useAPI('password-reset', {
                method: 'PUT',
                body: payload,
                server: false,
            })

            if (data.value && data.value.success) {
                this.resetPasswordEmail = data.value.email || ''
                return true
            }

            if (error.value && error.value.data?.detail) {
                console.log(error.value.data?.detail)
                this.resetPasswordError = error.value.data as ServerResponseError
                return false
            }
        },
        logout() {
            const translateStore = useTranslateStore()
            const appStore = useAppStore()
            localStorage.removeItem('access_token')
            localStorage.removeItem('refresh_token')
            localStorage.removeItem('seenNotificationPopup')
            appStore.setShowNotificationPopup(false)
            translateStore.clearTranslateText()
            translateStore.clearInputFile()
            translateStore.setShowAccountVerifyWarning(false)
            appStore.changeChatGPTVersion('gpt-4o-mini')
            this.user = undefined
        },
        async fetchUserInfo() {
            this.isLoggingIn = true
            const { data, error }: { data: Ref<UserInfoResponse>; error: any } = await useAPI('me', {
                method: 'GET',
                // lazy: true,
                server: false,
            })

            // if(error?.value?.statusCode === 403) {
            //     this.logout()
            //     window.location.href = "/"
            // }
            if (data.value) {
                this.user = data.value
                this.user.current_plan = data.value.user_plan?.product?.id || data.value.user_plan?.plan?.id || ''
                this.user.tokens = data.value.user_token?.available_token || 0
                const notificationsStore = useNotificationsStore()
                notificationsStore.getNotifications(data.value.uuid)

                if (!data.value.is_active) {
                    const translateStore = useTranslateStore()
                    translateStore.setShowAccountVerifyWarning(true)
                }
            }
            this.isLoggingIn = false
        },

        async activateAccount(token: string) {
            this.isLoggingIn = true
            const { data, error }: { data: Ref<LoginResponse>; error: any } = await useAPI('activate-account', {
                method: 'PUT',
                body: { token },
                server: false,
            })
            this.isLoggingIn = false

            if (data.value?.access_token) {
                localStorage.setItem('access_token', data.value.access_token)
                return data.value.access_token
            }
            if (error.value && error.value.data?.detail) {
                this.activateError = error.value.data as ServerResponseError
                return false
            }
            this.isError = true
        },

        async syncUserTokenInfo() {
            const { data, error }: { data: Ref<UserInfoResponse>; error: any } = await useAPI('me', {
                method: 'GET',
                // lazy: true,
                server: false,
            })

            if (data.value) {
                this.user = data.value
                this.user.current_plan = data.value.user_plan?.product?.id || data.value.user_plan?.plan?.id || ''
                this.user.tokens = data.value.user_token?.available_token || 0
            }
        },

        async resendVerifyEmail() {
            this.isResendingActivation = true
            const { data, error }: { data: Ref<string>; error: any } = await useAPI('resend-activation', {
                method: 'POST',
                // lazy: true,
                server: false,
                body: {
                    email: this.user?.email,
                },
            })
            this.isResendingActivation = false
        },

        setIsExpired(value: boolean) {
            this.isExpired = value
        },

        loginAsGuest() {
            this.user = {
                email: '<EMAIL>',
                user_token: {
                    plan_token: 99999999,
                    purchased_token: 0,
                    locked_token: 0,
                    available_token: 99999999,
                },
                user_plan: {
                    id: 8,
                    plan: { id: 'PP0002', name: 'Professional plan', type: 1 },
                    is_subscribe: false,
                    expire_at: '2023-11-18T15:26:20',
                },
                is_active: true,
                is_superuser: false,
                full_name: 'Guest',
                status: 2,
                id: 8,
                uuid: '4217733a-218d-11ee-b112-0a58a9feac02',
                current_plan: 'PP0002',
                tokens: 99999999,
                isGuest: true,
            }
        },

        logoutGuest() {
            if (this.user?.isGuest) {
                this.user = undefined
            }
        },

        async signInWithGoogle(invitation_code?: string | undefined) {
            this.isLoggingIn = true
            this.isSigningUp = true
            this.isError = false
            this.signInError = null
            const provider = new GoogleAuthProvider()
            const { auth } = useFirebase()
            try {
                const result = await signInWithPopup(auth, provider)
                console.log('🚀 ~ file: LoginByGoogle.vue:45 ~ .then ~ result:', result)
                // This gives you a Google Access Token. You can use it to access the Google API.
                const credential = GoogleAuthProvider.credentialFromResult(result)
                const token = credential.accessToken
                const { data, error }: { data: Ref<any>; error: any } = await useAPI('google-login-v2', {
                    method: 'POST',
                    // lazy: true,
                    server: false,
                    body: {
                        google_access_token: token,
                        invitation_code: invitation_code,
                    },
                })

                if (data.value?.access_token) {
                    localStorage.setItem('access_token', data.value.access_token)
                    if (data.value?.refresh_token) {
                        localStorage.setItem('refresh_token', data.value.refresh_token)
                    }
                    // The signed-in user info.
                    const user = result.user
                    // fake user
                    this.user = {
                        email: user.email || '',
                        full_name: user.displayName || '',
                        is_active: true,
                        is_superuser: false,
                        id: 0,
                        tokens: 0,
                        current_plan: '',
                        avatar: user.photoURL || '',
                    }
                    return true
                }

                if (error.value && error.value.data) {
                    console.log(error.value.data)
                    this.signInError = error.value.data as ServerResponseError
                    return false
                }

                return false
            } catch (error: any) {
                console.log('🚀 ~ file: LoginByGoogle.vue:55 ~ signInWithGoogle ~ error:', error)
                // Handle Errors here.
                const errorCode = error.code
                const errorMessage = error.message

                this.signInError = {
                    detail: {
                        error_code: errorCode,
                        error_message: errorMessage,
                    },
                }
                return false
            } finally {
                this.isLoggingIn = false
                this.isSigningUp = false
            }
        },
    },
})
